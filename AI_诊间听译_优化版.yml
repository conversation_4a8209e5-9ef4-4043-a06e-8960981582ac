app:
  description: 儿童牙科实时病历生成系统 - 优化版本，20-25秒内完成处理
  icon: 🦷
  icon_background: '#E3F2FD'
  mode: workflow
  name: 'AI 诊间听译_优化版'
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/volcengine_maas:0.0.20@aff2eaf766b1774519c3ca0e77de0b28c87b71cce6390b22a103518e8a5e4ae7
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: code
      id: start-preprocessor
      selected: false
      source: 'start'
      sourceHandle: source
      target: 'preprocessor'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: llm
      id: preprocessor-basic-extractor
      selected: false
      source: 'preprocessor'
      sourceHandle: source
      target: 'basic_extractor'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: llm
      id: preprocessor-diagnosis-matcher
      selected: false
      source: 'preprocessor'
      sourceHandle: source
      target: 'diagnosis_matcher'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: code
      id: basic-extractor-merger
      selected: false
      source: 'basic_extractor'
      sourceHandle: source
      target: 'merger'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: code
      id: diagnosis-matcher-merger
      selected: false
      source: 'diagnosis_matcher'
      sourceHandle: source
      target: 'merger'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: end
      id: merger-end
      selected: false
      source: 'merger'
      sourceHandle: source
      target: 'end'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: '实时音频转录后的对话文本输入'
        selected: false
        title: 开始
        type: start
        variables:
        - label: 对话文本
          max_length: 5000
          options: []
          required: true
          type: paragraph
          variable: dialog_text
        - label: 历史数据
          max_length: 500
          options: []
          required: false
          type: paragraph
          variable: history_data
        - label: 挂号类型（预检、治疗）
          max_length: 50
          options: []
          required: false
          type: text-input
          variable: type
      height: 140
      id: 'start'
      position:
        x: 50
        y: 100
      positionAbsolute:
        x: 50
        y: 100
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        code: |
          import time
          import hashlib
          import re

          def extract_patient_type(dialog_text: str, type_hint: str) -> str:
              """快速患者类型判断"""
              if type_hint and "预检" in type_hint:
                  return "预检患者"

              # 关键词快速判断
              treatment_keywords = ["补牙", "拔牙", "治疗", "手术", "根管", "充填", "修复"]
              if any(keyword in dialog_text for keyword in treatment_keywords):
                  return "治疗患者"

              return "预检患者"

          def extract_key_symptoms(dialog_text: str) -> list:
              """提取关键症状"""
              symptom_keywords = ["疼痛", "牙疼", "肿胀", "出血", "松动", "龋齿", "蛀牙", "虫牙"]
              found_symptoms = []
              for keyword in symptom_keywords:
                  if keyword in dialog_text:
                      found_symptoms.append(keyword)
              return found_symptoms

          def main(dialog_text: str, history_data: str, type_hint: str) -> dict:
              """预处理器 - 快速分析和数据准备"""
              start_time = time.time()

              # 文本清理
              cleaned_text = re.sub(r'\s+', ' ', dialog_text.strip())

              # 快速分析
              patient_type = extract_patient_type(cleaned_text, type_hint)
              key_symptoms = extract_key_symptoms(cleaned_text)

              # 生成处理标识
              content_hash = hashlib.md5(f"{cleaned_text}{patient_type}".encode()).hexdigest()[:8]

              result = {
                  "cleaned_dialog": cleaned_text,
                  "patient_type": patient_type,
                  "key_symptoms": key_symptoms,
                  "history_data": history_data or "",
                  "content_hash": content_hash,
                  "processing_start": start_time
              }

              return result
        code_language: python3
        desc: '快速预处理和患者类型判断，预计耗时2-3秒'
        outputs:
          cleaned_dialog:
            children: null
            type: string
          patient_type:
            children: null
            type: string
          key_symptoms:
            children: null
            type: array
          history_data:
            children: null
            type: string
          content_hash:
            children: null
            type: string
          processing_start:
            children: null
            type: number
        selected: false
        title: 预处理器
        type: code
        variables:
        - value_selector:
          - 'start'
          - dialog_text
          variable: dialog_text
        - value_selector:
          - 'start'
          - history_data
          variable: history_data
        - value_selector:
          - 'start'
          - type
          variable: type_hint
      height: 88
      id: 'preprocessor'
      position:
        x: 350
        y: 100
      positionAbsolute:
        x: 350
        y: 100
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        context:
          enabled: true
          variable_selector:
          - 'preprocessor'
          - cleaned_dialog
        desc: '快速提取基础病历信息，使用轻量级模型'
        model:
          completion_params:
            temperature: 0.1
            max_tokens: 800
          mode: chat
          name: gpt-3.5-turbo
          provider: openai
        prompt_template:
        - id: system-prompt
          role: system
          text: |
            你是儿童口腔科医学助手，负责从对话中快速提取基础病历信息。
            严格按照以下JSON格式输出，未提及的信息输出"不详"或"暂无"：

            {
              "主诉": "患者本次就诊的主要原因，20字以内",
              "现病史": "症状发展过程的详细描述",
              "既往史": "既往疾病史、手术史、外伤史等",
              "药物过敏史": "药物过敏情况",
              "体格检查": "口腔检查的客观发现",
              "辅助检查": "影像学检查结果"
            }

            对话内容：{{#preprocessor.cleaned_dialog#}}
            历史数据：{{#preprocessor.history_data#}}
            患者类型：{{#preprocessor.patient_type#}}
        selected: false
        title: 基础信息提取
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: 'basic_extractor'
      position:
        x: 650
        y: 50
      positionAbsolute:
        x: 650
        y: 50
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        context:
          enabled: true
          variable_selector:
          - 'preprocessor'
          - cleaned_dialog
        desc: '基于症状快速匹配诊断和治疗方案'
        model:
          completion_params:
            temperature: 0.2
            max_tokens: 1000
          mode: chat
          name: deepseek-v3
          provider: langgenius/volcengine_maas/volcengine_maas
        prompt_template:
        - id: system-prompt
          role: system
          text: |
            你是儿童口腔科实习生，负责快速生成诊断和治疗方案。

            ## 任务
            1. 根据对话内容判断初步诊断
            2. 匹配合适的治疗方案
            3. 提供注意事项

            ## 输出格式
            #### 初步诊断：
            [部位] + [诊断内容]

            #### 处理：
            [具体治疗方案]

            #### 注意事项：
            [重要提醒事项]

            ## 输入信息
            对话内容：{{#preprocessor.cleaned_dialog#}}
            患者类型：{{#preprocessor.patient_type#}}
            关键症状：{{#preprocessor.key_symptoms#}}

            ## 常用诊断术语
            龋齿、乳牙龋、窝沟封闭、正常乳牙列、正常替牙列、乳牙滞留、慢性根尖周炎、慢性牙髓炎、急性牙髓炎、牙龈炎、色素沉着、乳牙外伤、粘液腺囊肿、舌系带过短、唇系带异常
        selected: false
        title: 诊断模板匹配
        type: llm
        variables: []
        vision:
          enabled: false
      height: 88
      id: 'diagnosis_matcher'
      position:
        x: 650
        y: 150
      positionAbsolute:
        x: 650
        y: 150
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        code: |
          import json
          import time

          def parse_json_safely(text: str) -> dict:
              """安全解析JSON，失败时返回默认结构"""
              try:
                  # 尝试直接解析
                  return json.loads(text)
              except:
                  # 如果失败，尝试提取JSON部分
                  import re
                  json_match = re.search(r'\{.*\}', text, re.DOTALL)
                  if json_match:
                      try:
                          return json.loads(json_match.group())
                      except:
                          pass

                  # 返回默认结构
                  return {
                      "主诉": "口腔检查",
                      "现病史": "详见对话记录",
                      "既往史": "既往史不详",
                      "药物过敏史": "不详",
                      "体格检查": "详见对话记录",
                      "辅助检查": "暂无"
                  }

          def format_medical_record(basic_info_text: str, diagnosis_text: str, start_time: float) -> dict:
              """格式化最终病历输出"""

              # 解析基础信息
              basic_info = parse_json_safely(basic_info_text)

              # 清理诊断文本中的markdown标记
              diagnosis_clean = diagnosis_text.replace('```', '').replace('markdown', '').strip()

              # 组装完整病历
              medical_record = f"""#### 主诉：
          {basic_info.get('主诉', '口腔检查')}

          #### 现病史：
          {basic_info.get('现病史', '详见对话记录')}

          #### 既往史, 个人史, 家族史：
          {basic_info.get('既往史', '既往史不详')}

          #### 药物过敏史：
          {basic_info.get('药物过敏史', '不详')}

          #### 体格检查：
          {basic_info.get('体格检查', '详见对话记录')}

          #### 辅助检查：
          {basic_info.get('辅助检查', '暂无')}

          {diagnosis_clean}"""

              # 计算处理时间
              processing_time = time.time() - start_time

              return {
                  "result": medical_record,
                  "processing_time": f"{processing_time:.2f}秒",
                  "status": "success" if processing_time < 25 else "timeout_warning"
              }

          def main(basic_info: str, diagnosis_info: str, start_time: float) -> dict:
              """智能合并器主函数"""
              return format_medical_record(basic_info, diagnosis_info, start_time)
        code_language: python3
        desc: '智能合并基础信息和诊断结果，生成标准格式病历'
        outputs:
          result:
            children: null
            type: string
          processing_time:
            children: null
            type: string
          status:
            children: null
            type: string
        selected: false
        title: 智能合并器
        type: code
        variables:
        - value_selector:
          - 'basic_extractor'
          - text
          variable: basic_info
        - value_selector:
          - 'diagnosis_matcher'
          - text
          variable: diagnosis_info
        - value_selector:
          - 'preprocessor'
          - processing_start
          variable: start_time
      height: 88
      id: 'merger'
      position:
        x: 950
        y: 100
      positionAbsolute:
        x: 950
        y: 100
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    - data:
        desc: '输出最终的结构化病历'
        outputs:
        - value_selector:
          - 'merger'
          - result
          variable: medical_record
        - value_selector:
          - 'merger'
          - processing_time
          variable: processing_time
        - value_selector:
          - 'merger'
          - status
          variable: status
        selected: false
        title: 结束
        type: end
      height: 88
      id: 'end'
      position:
        x: 1250
        y: 100
      positionAbsolute:
        x: 1250
        y: 100
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 242
    viewport:
      x: 0
      y: 0
      zoom: 0.8
