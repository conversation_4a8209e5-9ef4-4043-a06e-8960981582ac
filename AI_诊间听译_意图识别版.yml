app:
  description: 儿童牙科实时病历生成系统 - 意图识别版本，智能匹配知识库模板
  icon: 🦷
  icon_background: '#E3F2FD'
  mode: workflow
  name: 'AI 诊间听译_意图识别版'
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/deepseek:0.0.5@fd6efd37c2a931911de8ab9ca3ba2da303bef146d45ee87ad896b04b36d09403
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/openai:0.0.26@c1e643ac6a7732f6333a783320b4d3026fa5e31d8e7026375b98d44418d33f26
kind: app
version: 0.3.1
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      enabled: false
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: code
      id: start-preprocessor
      source: start
      sourceHandle: source
      target: preprocessor
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: llm
      id: preprocessor-intent-classifier
      source: preprocessor
      sourceHandle: source
      target: intent_classifier
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: knowledge-retrieval
      id: intent-classifier-knowledge-retrieval
      source: intent_classifier
      sourceHandle: source
      target: knowledge_retrieval
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: knowledge-retrieval
        targetType: llm
      id: knowledge-retrieval-diagnosis-matcher
      source: knowledge_retrieval
      sourceHandle: source
      target: diagnosis_matcher
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: llm
      id: preprocessor-basic-extractor
      source: preprocessor
      sourceHandle: source
      target: basic_extractor
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: code
      id: basic-extractor-merger
      source: basic_extractor
      sourceHandle: source
      target: merger
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: '实时音频转录后的对话文本输入'
        selected: false
        title: 开始
        type: start
        variables:
        - label: 对话文本
          max_length: 5000
          options: []
          required: true
          type: paragraph
          variable: dialog_text
        - label: 历史数据
          max_length: 500
          options: []
          required: false
          type: paragraph
          variable: history_data
        - label: 挂号类型（预检、治疗）
          max_length: 50
          options: []
          required: false
          type: text-input
          variable: type
      height: 170
      id: start
      position:
        x: 50
        y: 100
      positionAbsolute:
        x: 50
        y: 100
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: |
          import time
          import hashlib
          import re

          def extract_patient_type(dialog_text: str, type_hint: str) -> str:
              """快速患者类型判断"""
              if type_hint and "预检" in type_hint:
                  return "预检患者"

              # 关键词快速判断
              treatment_keywords = ["补牙", "拔牙", "治疗", "手术", "根管", "充填", "修复"]
              if any(keyword in dialog_text for keyword in treatment_keywords):
                  return "治疗患者"

              return "预检患者"

          def extract_key_symptoms(dialog_text: str) -> str:
              """提取关键症状"""
              symptom_keywords = ["疼痛", "牙疼", "肿胀", "出血", "松动", "龋齿", "蛀牙", "虫牙"]
              found_symptoms = []
              for keyword in symptom_keywords:
                  if keyword in dialog_text:
                      found_symptoms.append(keyword)
              return ", ".join(found_symptoms) if found_symptoms else "无明显症状"

          def main(dialog_text: str, history_data: str, type_hint: str) -> dict:
              """预处理器 - 快速分析和数据准备"""
              start_time = time.time()

              # 文本清理
              cleaned_text = re.sub(r'\s+', ' ', dialog_text.strip())

              # 快速分析
              patient_type = extract_patient_type(cleaned_text, type_hint)
              key_symptoms = extract_key_symptoms(cleaned_text)

              # 生成处理标识
              content_hash = hashlib.md5(f"{cleaned_text}{patient_type}".encode()).hexdigest()[:8]

              result = {
                  "cleaned_dialog": cleaned_text,
                  "patient_type": patient_type,
                  "key_symptoms": key_symptoms,
                  "history_data": history_data or "",
                  "content_hash": content_hash,
                  "processing_start": start_time
              }

              return result
        code_language: python3
        desc: '快速预处理和患者类型判断，预计耗时2-3秒'
        outputs:
          cleaned_dialog:
            children: null
            type: string
          content_hash:
            children: null
            type: string
          history_data:
            children: null
            type: string
          key_symptoms:
            children: null
            type: string
          patient_type:
            children: null
            type: string
          processing_start:
            children: null
            type: number
        selected: false
        title: 预处理器
        type: code
        variables:
        - value_selector:
          - start
          - dialog_text
          variable: dialog_text
        - value_selector:
          - start
          - history_data
          variable: history_data
        - value_selector:
          - start
          - type
          variable: type_hint
      height: 98
      id: preprocessor
      position:
        x: 350
        y: 100
      positionAbsolute:
        x: 350
        y: 100
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
        desc: '智能识别患者意图和诊疗需求，生成精准的知识库查询'
        model:
          completion_params:
            max_tokens: 300
            temperature: 0.1
          mode: chat
          name: deepseek-chat
          provider: langgenius/deepseek/deepseek
        prompt_template:
        - id: system-prompt
          role: system
          text: |
            你是儿童口腔科意图识别专家，负责分析对话内容并识别患者的具体诊疗意图。

            ## 任务
            分析对话内容，识别以下意图类别并生成知识库查询关键词：

            ### 意图分类：
            1. **预检咨询** - 常规检查、预防性治疗
            2. **治疗需求** - 具体疾病治疗、手术操作
            3. **症状诊断** - 疼痛、不适症状分析
            4. **复诊随访** - 治疗后复查、效果评估
            5. **紧急处理** - 急性症状、外伤处理

            ### 输出格式（严格按此格式）：
            意图类别：[选择上述5类之一]
            查询关键词：[3-5个关键词，用逗号分隔]
            患者类型：{{#preprocessor.patient_type#}}
            主要症状：{{#preprocessor.key_symptoms#}}

            ### 输入信息：
            对话内容：{{#preprocessor.cleaned_dialog#}}
            挂号类型：{{#start.type#}}

            ### 示例：
            意图类别：治疗需求
            查询关键词：龋齿,补牙,充填治疗,乳牙
            患者类型：治疗患者
            主要症状：龋齿,疼痛
        selected: false
        title: 意图识别
        type: llm
        variables: []
        vision:
          enabled: false
      height: 118
      id: intent_classifier
      position:
        x: 500
        y: 150
      positionAbsolute:
        x: 500
        y: 150
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        isInLoop: false
        sourceType: llm
        targetType: code
      id: diagnosis-matcher-merger
      source: diagnosis_matcher
      sourceHandle: source
      target: merger
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: end
      id: merger-end
      source: merger
      sourceHandle: source
      target: end
      targetHandle: target
      type: custom
      zIndex: 0
