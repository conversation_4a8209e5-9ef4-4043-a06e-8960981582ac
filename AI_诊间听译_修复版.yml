app:
  description: 儿童牙科实时病历生成系统 - 修复版本，20-25秒内完成处理
  icon: 🦷
  icon_background: '#E3F2FD'
  mode: workflow
  name: AI 诊间听译_修复版
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/deepseek:0.0.5@fd6efd37c2a931911de8ab9ca3ba2da303bef146d45ee87ad896b04b36d09403
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/openai:0.0.26@c1e643ac6a7732f6333a783320b4d3026fa5e31d8e7026375b98d44418d33f26
kind: app
version: 0.3.1
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      enabled: false
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: code
      id: start-preprocessor
      source: start
      sourceHandle: source
      target: preprocessor
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: llm
      id: preprocessor-intent-classifier
      source: preprocessor
      sourceHandle: source
      target: intent_classifier
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: knowledge-retrieval
      id: intent-classifier-knowledge-retrieval
      source: intent_classifier
      sourceHandle: source
      target: knowledge_retrieval
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: knowledge-retrieval
        targetType: llm
      id: knowledge-retrieval-diagnosis-matcher
      source: knowledge_retrieval
      sourceHandle: source
      target: diagnosis_matcher
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: llm
      id: preprocessor-basic-extractor
      source: preprocessor
      sourceHandle: source
      target: basic_extractor
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: code
      id: basic-extractor-merger
      source: basic_extractor
      sourceHandle: source
      target: merger
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: 实时音频转录后的对话文本输入
        selected: false
        title: 开始
        type: start
        variables:
        - label: 对话文本
          max_length: 5000
          options: []
          required: true
          type: paragraph
          variable: dialog_text
        - label: 历史数据
          max_length: 500
          options: []
          required: false
          type: paragraph
          variable: history_data
        - label: 挂号类型（预检、治疗）
          max_length: 50
          options: []
          required: false
          type: text-input
          variable: type
      height: 170
      id: start
      position:
        x: 50
        y: 100
      positionAbsolute:
        x: 50
        y: 100
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import time\nimport hashlib\nimport re\n\ndef extract_patient_type(dialog_text: str, type_hint: str) -> str:\n    if type_hint and \"预检\" in type_hint:\n        return \"预检患者\"\n    treatment_keywords = [\"补牙\", \"拔牙\", \"治疗\", \"手术\", \"根管\", \"充填\", \"修复\"]\n    if any(keyword in dialog_text for keyword in treatment_keywords):\n        return \"治疗患者\"\n    return \"预检患者\"\n\ndef extract_key_symptoms(dialog_text: str) -> str:\n    symptom_keywords = [\"疼痛\", \"牙疼\", \"肿胀\", \"出血\", \"松动\", \"龋齿\", \"蛀牙\", \"虫牙\"]\n    found_symptoms = []\n    for keyword in symptom_keywords:\n        if keyword in dialog_text:\n            found_symptoms.append(keyword)\n    return \", \".join(found_symptoms) if found_symptoms else \"无明显症状\"\n\ndef main(dialog_text: str, history_data: str, type_hint: str) -> dict:\n    start_time = time.time()\n    cleaned_text = re.sub(r'\\s+', ' ', dialog_text.strip())\n    patient_type = extract_patient_type(cleaned_text, type_hint)\n    key_symptoms = extract_key_symptoms(cleaned_text)\n    content_hash = hashlib.md5(f\"{cleaned_text}{patient_type}\".encode()).hexdigest()[:8]\n    result = {\n        \"cleaned_dialog\": cleaned_text,\n        \"patient_type\": patient_type,\n        \"key_symptoms\": key_symptoms,\n        \"history_data\": history_data or \"\",\n        \"content_hash\": content_hash,\n        \"processing_start\": start_time\n    }\n    return result"
        code_language: python3
        desc: 快速预处理和患者类型判断，预计耗时2-3秒
        outputs:
          cleaned_dialog:
            children: null
            type: string
          content_hash:
            children: null
            type: string
          history_data:
            children: null
            type: string
          key_symptoms:
            children: null
            type: string
          patient_type:
            children: null
            type: string
          processing_start:
            children: null
            type: number
        selected: false
        title: 预处理器
        type: code
        variables:
        - value_selector:
          - start
          - dialog_text
          variable: dialog_text
        - value_selector:
          - start
          - history_data
          variable: history_data
        - value_selector:
          - start
          - type
          variable: type_hint
      height: 98
      id: preprocessor
      position:
        x: 350
        y: 100
      positionAbsolute:
        x: 350
        y: 100
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
        desc: 智能识别患者意图和诊疗需求，生成精准的知识库查询
        model:
          completion_params:
            max_tokens: 300
            temperature: 0.1
          mode: chat
          name: deepseek-chat
          provider: langgenius/deepseek/deepseek
        prompt_template:
        - id: system-prompt
          role: system
          text: "你是儿童口腔科意图识别专家。分析对话内容，识别意图类别并生成知识库查询关键词。\n\n意图分类：\n1. 预检咨询 - 常规检查、预防性治疗\n2. 治疗需求 - 具体疾病治疗、手术操作\n3. 症状诊断 - 疼痛、不适症状分析\n4. 复诊随访 - 治疗后复查、效果评估\n5. 紧急处理 - 急性症状、外伤处理\n\n输出格式：\n意图类别：[选择上述5类之一]\n查询关键词：[3-5个关键词，用逗号分隔]\n患者类型：{{#preprocessor.patient_type#}}\n主要症状：{{#preprocessor.key_symptoms#}}\n\n输入信息：\n对话内容：{{#preprocessor.cleaned_dialog#}}\n挂号类型：{{#start.type#}}"
        selected: false
        title: 意图识别
        type: llm
        variables: []
        vision:
          enabled: false
      height: 118
      id: intent_classifier
      position:
        x: 500
        y: 150
      positionAbsolute:
        x: 500
        y: 150
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        dataset_ids:
        - aRX8fkJY52HKT2Vtgj0WCr5zrHt7o0HDHpuEUtkL5BOhtqtx0jbLdTgAciIhc5fn
        desc: 基于意图识别结果，精准检索相关病历模板和治疗方案
        multiple_retrieval_config:
          reranking_enable: true
          reranking_mode: weighted_score
          top_k: 4
          weights:
            keyword_setting:
              keyword_weight: 0
            vector_setting:
              embedding_model_name: text-embedding-3-large
              embedding_provider_name: langgenius/openai/openai
              vector_weight: 1
        query_variable_selector:
        - intent_classifier
        - text
        retrieval_mode: multiple
        selected: false
        title: 知识检索
        type: knowledge-retrieval
      height: 92
      id: knowledge_retrieval
      position:
        x: 700
        y: 150
      positionAbsolute:
        x: 700
        y: 150
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        isInLoop: false
        sourceType: llm
        targetType: code
      id: diagnosis-matcher-merger
      source: diagnosis_matcher
      sourceHandle: source
      target: merger
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: end
      id: merger-end
      source: merger
      sourceHandle: source
      target: end
      targetHandle: target
      type: custom
      zIndex: 0
