app:
  description: 儿童牙科实时病历生成系统 - 优化版本，20-25秒内完成处理
  icon: 🦷
  icon_background: '#E3F2FD'
  mode: workflow
  name: AI 诊间听译_优化版
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/deepseek:0.0.5@fd6efd37c2a931911de8ab9ca3ba2da303bef146d45ee87ad896b04b36d09403
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/openai:0.0.26@c1e643ac6a7732f6333a783320b4d3026fa5e31d8e7026375b98d44418d33f26
kind: app
version: 0.3.1
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: code
      id: start-preprocessor
      selected: false
      source: start
      sourceHandle: source
      target: preprocessor
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: code
      id: basic-extractor-merger
      selected: false
      source: basic_extractor
      sourceHandle: source
      target: merger
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: code
      id: diagnosis-matcher-merger
      selected: false
      source: diagnosis_matcher
      sourceHandle: source
      target: merger
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: end
      id: merger-end
      selected: false
      source: merger
      sourceHandle: source
      target: end
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: llm
      id: preprocessor-intent-classifier
      source: preprocessor
      sourceHandle: source
      target: intent_classifier
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: knowledge-retrieval
      id: intent-classifier-knowledge-retrieval
      source: intent_classifier
      sourceHandle: source
      target: '1753924296732'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: knowledge-retrieval
        targetType: llm
      id: 1753924296732-source-diagnosis_matcher-target
      source: '1753924296732'
      sourceHandle: source
      target: diagnosis_matcher
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: llm
      id: preprocessor-source-basic_extractor-target
      source: preprocessor
      sourceHandle: source
      target: basic_extractor
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: 实时音频转录后的对话文本输入
        selected: false
        title: 开始
        type: start
        variables:
        - label: 对话文本
          max_length: 5000
          options: []
          required: true
          type: paragraph
          variable: dialog_text
        - label: 历史数据
          max_length: 500
          options: []
          required: false
          type: paragraph
          variable: history_data
        - label: 挂号类型（预检、治疗）
          max_length: 50
          options: []
          required: false
          type: text-input
          variable: type
      height: 170
      id: start
      position:
        x: 50
        y: 100
      positionAbsolute:
        x: 50
        y: 100
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import time\nimport hashlib\nimport re\n\ndef extract_patient_type(dialog_text:\
          \ str, type_hint: str) -> str:\n    \"\"\"快速患者类型判断\"\"\"\n    if type_hint\
          \ and \"预检\" in type_hint:\n        return \"预检患者\"\n\n    # 关键词快速判断\n \
          \   treatment_keywords = [\"补牙\", \"拔牙\", \"治疗\", \"手术\", \"根管\", \"充填\"\
          , \"修复\"]\n    if any(keyword in dialog_text for keyword in treatment_keywords):\n\
          \        return \"治疗患者\"\n\n    return \"预检患者\"\n\ndef extract_key_symptoms(dialog_text:\
          \ str) -> str:\n    \"\"\"提取关键症状\"\"\"\n    symptom_keywords = [\"疼痛\",\
          \ \"牙疼\", \"肿胀\", \"出血\", \"松动\", \"龋齿\", \"蛀牙\", \"虫牙\"]\n    found_symptoms\
          \ = []\n    for keyword in symptom_keywords:\n        if keyword in dialog_text:\n\
          \            found_symptoms.append(keyword)\n    return \", \".join(found_symptoms)\
          \ if found_symptoms else \"无明显症状\"\n\ndef main(dialog_text: str, history_data:\
          \ str, type_hint: str) -> dict:\n    \"\"\"预处理器 - 快速分析和数据准备\"\"\"\n    start_time\
          \ = time.time()\n\n    # 文本清理\n    cleaned_text = re.sub(r'\\s+', ' ', dialog_text.strip())\n\
          \n    # 快速分析\n    patient_type = extract_patient_type(cleaned_text, type_hint)\n\
          \    key_symptoms = extract_key_symptoms(cleaned_text)\n\n    # 生成处理标识\n\
          \    content_hash = hashlib.md5(f\"{cleaned_text}{patient_type}\".encode()).hexdigest()[:8]\n\
          \n    result = {\n        \"cleaned_dialog\": cleaned_text,\n        \"\
          patient_type\": patient_type,\n        \"key_symptoms\": key_symptoms,\n\
          \        \"history_data\": history_data or \"\",\n        \"content_hash\"\
          : content_hash,\n        \"processing_start\": start_time\n    }\n\n   \
          \ return result\n"
        code_language: python3
        desc: 快速预处理和患者类型判断，预计耗时2-3秒
        outputs:
          cleaned_dialog:
            children: null
            type: string
          content_hash:
            children: null
            type: string
          history_data:
            children: null
            type: string
          key_symptoms:
            children: null
            type: string
          patient_type:
            children: null
            type: string
          processing_start:
            children: null
            type: number
        selected: true
        title: 预处理器
        type: code
        variables:
        - value_selector:
          - start
          - dialog_text
          variable: dialog_text
        - value_selector:
          - start
          - history_data
          variable: history_data
        - value_selector:
          - start
          - type
          variable: type_hint
      height: 98
      id: preprocessor
      position:
        x: 350
        y: 100
      positionAbsolute:
        x: 350
        y: 100
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
        desc: '智能识别患者意图和诊疗需求，生成精准的知识库查询'
        model:
          completion_params:
            max_tokens: 300
            temperature: 0.1
          mode: chat
          name: deepseek-chat
          provider: langgenius/deepseek/deepseek
        prompt_template:
        - id: system-prompt
          role: system
          text: |
            你是儿童口腔科意图识别专家，负责分析对话内容并识别患者的具体诊疗意图。

            ## 任务
            分析对话内容，识别以下意图类别并生成知识库查询关键词：

            ### 意图分类：
            1. **预检咨询** - 常规检查、预防性治疗
            2. **治疗需求** - 具体疾病治疗、手术操作
            3. **症状诊断** - 疼痛、不适症状分析
            4. **复诊随访** - 治疗后复查、效果评估
            5. **紧急处理** - 急性症状、外伤处理

            ### 输出格式（严格按此格式）：
            意图类别：[选择上述5类之一]
            查询关键词：[3-5个关键词，用逗号分隔]
            患者类型：{{#preprocessor.patient_type#}}
            主要症状：{{#preprocessor.key_symptoms#}}

            ### 输入信息：
            对话内容：{{#preprocessor.cleaned_dialog#}}
            挂号类型：{{#start.type#}}

            ### 示例：
            意图类别：治疗需求
            查询关键词：龋齿,补牙,充填治疗,乳牙
            患者类型：治疗患者
            主要症状：龋齿,疼痛
        selected: false
        title: 意图识别
        type: llm
        variables: []
        vision:
          enabled: false
      height: 118
      id: intent_classifier
      position:
        x: 500
        y: 150
      positionAbsolute:
        x: 500
        y: 150
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - preprocessor
          - cleaned_dialog
        desc: 快速提取基础病历信息，使用轻量级模型
        model:
          completion_params:
            max_tokens: 800
            temperature: 0.1
          mode: chat
          name: deepseek-chat
          provider: langgenius/deepseek/deepseek
        prompt_template:
        - id: system-prompt
          role: system
          text: "你是儿童口腔科医学助手，负责从对话中快速提取基础病历信息。\n严格按照以下JSON格式输出，未提及的信息输出\"不详\"或\"暂无\"\
            ：\n\n{\n  \"主诉\": \"患者本次就诊的主要原因，20字以内\",\n  \"现病史\": \"症状发展过程的详细描述\",\n\
            \  \"既往史\": \"既往疾病史、手术史、外伤史等\",\n  \"药物过敏史\": \"药物过敏情况\",\n  \"体格检查\"\
            : \"口腔检查的客观发现\",\n  \"辅助检查\": \"影像学检查结果\"\n}\n\n对话内容：{{#preprocessor.cleaned_dialog#}}\n\
            历史数据：{{#preprocessor.history_data#}}\n患者类型：{{#preprocessor.patient_type#}}\n"
        selected: false
        title: 基础信息提取
        type: llm
        variables: []
        vision:
          enabled: false
      height: 134
      id: basic_extractor
      position:
        x: 948.75
        y: 51.25
      positionAbsolute:
        x: 948.75
        y: 51.25
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - '1753924296732'
          - result
        desc: '基于意图识别和知识检索结果，生成精准的诊断和治疗方案'
        model:
          completion_params:
            max_tokens: 1000
            temperature: 0.2
          mode: chat
          name: deepseek-chat
          provider: langgenius/deepseek/deepseek
        prompt_template:
        - id: system-prompt
          role: system
          text: |
            你是儿童口腔科实习生，负责基于意图识别和知识检索结果生成精准的诊断和治疗方案。

            ## 任务流程
            1. 分析意图识别结果：{{#intent_classifier.text#}}
            2. 参考知识检索内容：{{#context#}}
            3. 结合对话信息生成标准化病历

            ## 输出格式（严格按此格式）
            #### 初步诊断：
            [部位] + [诊断内容]

            #### 处理：
            [具体治疗方案]

            #### 注意事项：
            [重要提醒事项]

            ## 输入信息
            对话内容：{{#preprocessor.cleaned_dialog#}}
            患者类型：{{#preprocessor.patient_type#}}
            关键症状：{{#preprocessor.key_symptoms#}}

            ## 要求
            - 严格按照知识库模板格式输出
            - 确保诊断术语专业准确
            - 治疗方案具体可操作
            - 注意事项针对儿童特点
        selected: false
        title: 诊断模板匹配
        type: llm
        variables: []
        vision:
          enabled: false
      height: 118
      id: diagnosis_matcher
      position:
        x: 941.2499999999999
        y: 256.25
      positionAbsolute:
        x: 941.2499999999999
        y: 256.25
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\nimport time\n\ndef parse_json_safely(text: str) -> dict:\n\
          \    \"\"\"安全解析JSON，失败时返回默认结构\"\"\"\n    try:\n        # 尝试直接解析\n      \
          \  return json.loads(text)\n    except:\n        # 如果失败，尝试提取JSON部分\n   \
          \     import re\n        json_match = re.search(r'\\{.*\\}', text, re.DOTALL)\n\
          \        if json_match:\n            try:\n                return json.loads(json_match.group())\n\
          \            except:\n                pass\n\n        # 返回默认结构\n       \
          \ return {\n            \"主诉\": \"口腔检查\",\n            \"现病史\": \"详见对话记录\"\
          ,\n            \"既往史\": \"既往史不详\",\n            \"药物过敏史\": \"不详\",\n   \
          \         \"体格检查\": \"详见对话记录\",\n            \"辅助检查\": \"暂无\"\n        }\n\
          \ndef format_medical_record(basic_info_text: str, diagnosis_text: str, start_time:\
          \ float) -> dict:\n    \"\"\"格式化最终病历输出\"\"\"\n\n    # 解析基础信息\n    basic_info\
          \ = parse_json_safely(basic_info_text)\n\n    # 清理诊断文本中的markdown标记\n   \
          \ diagnosis_clean = diagnosis_text.replace('```', '').replace('markdown',\
          \ '').strip()\n\n    # 组装完整病历\n    medical_record = f\"\"\"#### 主诉：\n{basic_info.get('主诉',\
          \ '口腔检查')}\n\n#### 现病史：\n{basic_info.get('现病史', '详见对话记录')}\n\n#### 既往史,\
          \ 个人史, 家族史：\n{basic_info.get('既往史', '既往史不详')}\n\n#### 药物过敏史：\n{basic_info.get('药物过敏史',\
          \ '不详')}\n\n#### 体格检查：\n{basic_info.get('体格检查', '详见对话记录')}\n\n#### 辅助检查：\n\
          {basic_info.get('辅助检查', '暂无')}\n\n{diagnosis_clean}\"\"\"\n\n    # 计算处理时间\n\
          \    processing_time = time.time() - start_time\n\n    return {\n      \
          \  \"result\": medical_record,\n        \"processing_time\": f\"{processing_time:.2f}秒\"\
          ,\n        \"status\": \"success\" if processing_time < 25 else \"timeout_warning\"\
          \n    }\n\ndef main(basic_info: str, diagnosis_info: str, start_time: float)\
          \ -> dict:\n    \"\"\"智能合并器主函数\"\"\"\n    return format_medical_record(basic_info,\
          \ diagnosis_info, start_time)\n"
        code_language: python3
        desc: 智能合并基础信息和诊断结果，生成标准格式病历
        outputs:
          processing_time:
            children: null
            type: string
          result:
            children: null
            type: string
          status:
            children: null
            type: string
        selected: false
        title: 智能合并器
        type: code
        variables:
        - value_selector:
          - basic_extractor
          - text
          variable: basic_info
        - value_selector:
          - diagnosis_matcher
          - text
          variable: diagnosis_info
        - value_selector:
          - preprocessor
          - processing_start
          variable: start_time
      height: 98
      id: merger
      position:
        x: 1250
        y: 100
      positionAbsolute:
        x: 1250
        y: 100
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: 输出最终的结构化病历
        outputs:
        - value_selector:
          - merger
          - result
          variable: medical_record
        - value_selector:
          - merger
          - processing_time
          variable: processing_time
        - value_selector:
          - merger
          - status
          variable: status
        selected: false
        title: 结束
        type: end
      height: 170
      id: end
      position:
        x: 1550
        y: 100
      positionAbsolute:
        x: 1550
        y: 100
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        dataset_ids:
        - aRX8fkJY52HKT2Vtgj0WCr5zrHt7o0HDHpuEUtkL5BOhtqtx0jbLdTgAciIhc5fn
        desc: '基于意图识别结果，精准检索相关病历模板和治疗方案'
        multiple_retrieval_config:
          reranking_enable: true
          reranking_mode: weighted_score
          top_k: 4
          weights:
            keyword_setting:
              keyword_weight: 0
            vector_setting:
              embedding_model_name: text-embedding-3-large
              embedding_provider_name: langgenius/openai/openai
              vector_weight: 1
        query_variable_selector:
        - intent_classifier
        - text
        retrieval_mode: multiple
        selected: false
        title: 知识检索
        type: knowledge-retrieval
      height: 92
      id: '1753924296732'
      position:
        x: 700
        y: 150
      positionAbsolute:
        x: 700
        y: 150
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 218.4000000000001
      y: 299
      zoom: 0.8
