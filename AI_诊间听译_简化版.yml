app:
  description: 儿童牙科实时病历生成系统 - 简化版本，包含意图识别功能
  icon: 🦷
  icon_background: '#E3F2FD'
  mode: workflow
  name: AI 诊间听译_简化版
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/deepseek:0.0.5@fd6efd37c2a931911de8ab9ca3ba2da303bef146d45ee87ad896b04b36d09403
kind: app
version: 0.3.1
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      enabled: false
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: start-intent-classifier
      source: start
      sourceHandle: source
      target: intent_classifier
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: knowledge-retrieval
      id: intent-classifier-knowledge-retrieval
      source: intent_classifier
      sourceHandle: source
      target: knowledge_retrieval
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: knowledge-retrieval
        targetType: llm
      id: knowledge-retrieval-diagnosis-matcher
      source: knowledge_retrieval
      sourceHandle: source
      target: diagnosis_matcher
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: end
      id: diagnosis-matcher-end
      source: diagnosis_matcher
      sourceHandle: source
      target: end
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: 实时音频转录后的对话文本输入
        selected: false
        title: 开始
        type: start
        variables:
        - label: 对话文本
          max_length: 5000
          options: []
          required: true
          type: paragraph
          variable: dialog_text
        - label: 挂号类型
          max_length: 50
          options: []
          required: false
          type: text-input
          variable: type
      height: 170
      id: start
      position:
        x: 50
        y: 100
      positionAbsolute:
        x: 50
        y: 100
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
        desc: 智能识别患者意图和诊疗需求
        model:
          completion_params:
            max_tokens: 300
            temperature: 0.1
          mode: chat
          name: deepseek-chat
          provider: langgenius/deepseek/deepseek
        prompt_template:
        - id: system-prompt
          role: system
          text: "你是儿童口腔科意图识别专家。分析对话内容，识别意图类别并生成知识库查询关键词。\n\n意图分类：\n1. 预检咨询\n2. 治疗需求\n3. 症状诊断\n4. 复诊随访\n5. 紧急处理\n\n输出格式：\n意图类别：[选择上述5类之一]\n查询关键词：[3-5个关键词，用逗号分隔]\n\n输入信息：\n对话内容：{{#start.dialog_text#}}\n挂号类型：{{#start.type#}}"
        selected: false
        title: 意图识别
        type: llm
        variables: []
        vision:
          enabled: false
      height: 118
      id: intent_classifier
      position:
        x: 350
        y: 100
      positionAbsolute:
        x: 350
        y: 100
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        dataset_ids:
        - aRX8fkJY52HKT2Vtgj0WCr5zrHt7o0HDHpuEUtkL5BOhtqtx0jbLdTgAciIhc5fn
        desc: 基于意图识别结果检索相关病历模板
        multiple_retrieval_config:
          reranking_enable: true
          reranking_mode: weighted_score
          top_k: 4
          weights:
            keyword_setting:
              keyword_weight: 0
            vector_setting:
              embedding_model_name: text-embedding-3-large
              embedding_provider_name: langgenius/openai/openai
              vector_weight: 1
        query_variable_selector:
        - intent_classifier
        - text
        retrieval_mode: multiple
        selected: false
        title: 知识检索
        type: knowledge-retrieval
      height: 92
      id: knowledge_retrieval
      position:
        x: 650
        y: 100
      positionAbsolute:
        x: 650
        y: 100
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - knowledge_retrieval
          - result
        desc: 基于意图识别和知识检索结果生成病历
        model:
          completion_params:
            max_tokens: 1000
            temperature: 0.2
          mode: chat
          name: deepseek-chat
          provider: langgenius/deepseek/deepseek
        prompt_template:
        - id: system-prompt
          role: system
          text: "你是儿童口腔科医生，负责生成标准化病历。\n\n任务：\n1. 分析意图识别结果：{{#intent_classifier.text#}}\n2. 参考知识检索内容：{{#context#}}\n3. 生成标准化病历\n\n输出格式：\n#### 主诉：\n[患者本次就诊的主要原因]\n\n#### 现病史：\n[症状发展过程]\n\n#### 既往史, 个人史, 家族史：\n[相关病史]\n\n#### 药物过敏史：\n[过敏情况]\n\n#### 体格检查：\n[口腔检查发现]\n\n#### 辅助检查：\n[检查结果]\n\n#### 初步诊断：\n[诊断结果]\n\n#### 处理：\n[治疗方案]\n\n#### 注意事项：\n[重要提醒]\n\n输入信息：\n对话内容：{{#start.dialog_text#}}\n挂号类型：{{#start.type#}}"
        selected: false
        title: 病历生成
        type: llm
        variables: []
        vision:
          enabled: false
      height: 118
      id: diagnosis_matcher
      position:
        x: 950
        y: 100
      positionAbsolute:
        x: 950
        y: 100
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: 输出最终的结构化病历
        outputs:
        - value_selector:
          - diagnosis_matcher
          - text
          variable: medical_record
        selected: false
        title: 结束
        type: end
      height: 170
      id: end
      position:
        x: 1250
        y: 100
      positionAbsolute:
        x: 1250
        y: 100
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 0
      y: 0
      zoom: 0.8
